{
  description = "Python environment with Django and Uvicorn";

  inputs = {
    # Use the nixpkgs flake to access Python and other packages
    nixpkgs.url = "github:NixOS/nixpkgs";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils, ... }:
    flake-utils.lib.eachSystem [ "x86_64-linux" "aarch64-linux" ] (system: let
      pkgs = nixpkgs.legacyPackages.${system};
      python = pkgs.python3;
      uv = pkgs.uv;
      nushell = pkgs.nushell;

      # Define a virtualenv with the necessary packages
      myEnv = python.withPackages (ps: with ps; [ 
        uv
      ]);

    in {
      # The default package is the virtual environment with Python, Django, and Uvicorn
      defaultPackage = myEnv;

      # Provide a devShell to easily interact with the environment
      devShell = pkgs.mkShell {
        buildInputs = [ python uv nushell ];
        shell = "${nushell}/bin/nushell";  # Set Nushell as the shell to be used

        shellHook = ''
          # export PYTHONPATH=${myEnv}/lib/python${python.version}/site-packages
          if [ -d ".venv" ]; then source .venv/bin/activate; fi;
        '';
      };
    });
}
