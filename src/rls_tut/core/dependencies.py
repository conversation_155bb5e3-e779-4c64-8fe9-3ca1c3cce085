from fastapi import Depends, HTTPException
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON>Bearer
from jose import jwt
from jose import JW<PERSON>rror
from sqlalchemy.orm import Session

from rls_tut.database.models import User
from .database import SessionLocal
from .settings import Settings


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_settings():
    return Settings()


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")


def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db),
    settings: Settings = Depends(get_settings),
):
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=["HS256"])
        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(status_code=401, detail="Invalid token")
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=401, detail="User not found")
        return user
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
